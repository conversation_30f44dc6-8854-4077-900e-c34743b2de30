package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.network.town.TownImageSynchronizer;
import com.pokecobble.town.sound.SoundUtil;
import com.pokecobble.town.util.TownImageUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.text.Text;
import net.minecraft.text.Style;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Optimized screen for selecting a town image.
 */
public class TownImageScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private List<String> availableImages = new ArrayList<>();
    private int selectedImageIndex = -1;
    private static final int IMAGE_SIZE = 120; // Larger circles
    private static final int IMAGE_PADDING = 40; // More spacing
    private static final int MAX_IMAGES = 3; // Always show exactly 3 slots

    // Layout constants
    private static final int INSTRUCTION_HEIGHT = 100; // Space for upload instructions

    // Caching and optimization
    private static final Map<String, Identifier> CIRCULAR_MASK_CACHE = new HashMap<>();
    private static final Map<String, Long> LAST_IMAGE_UPDATE_TIME = new HashMap<>();
    private static final long IMAGE_UPDATE_COOLDOWN_MS = 5000; // 5 seconds cooldown between updates
    private boolean imagesLoaded = false;
    private boolean needsImageRefresh = true;
    private static final Executor IMAGE_LOADING_EXECUTOR = Executors.newSingleThreadExecutor();

    // Pre-calculated values for rendering
    private int panelWidth;
    private int panelHeight;
    private int panelX;
    private int panelY;
    private int totalWidth;
    private int startX;
    private int centerY;
    private int startIndex;

    // UI colors
    private static final int BACKGROUND_COLOR = 0xCC000000;
    private static final int HEADER_COLOR = 0xFF1E88E5;
    private static final int BORDER_COLOR = 0xFF42A5F5;
    private static final int SELECTED_COLOR = 0xFF4CAF50;
    private static final int UNSELECTED_COLOR = 0xFF333333;

    // Path to town images
    private static final String TOWN_IMAGES_PATH = "config/pokecobbleclaim/towns/";

    // Circular mask identifiers
    private static Identifier CIRCLE_MASK_IDENTIFIER;

    public TownImageScreen(Screen parent) {
        super(Text.translatable("screen.pokecobbleclaim.town_image"));
        this.parent = parent;

        // Get the player's town from ClientTownManager (client-side)
        this.town = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

        // Initialize the circular mask if not already created
        if (CIRCLE_MASK_IDENTIFIER == null) {
            createCircleMask();
        }
    }

    /**
     * Creates a circular mask texture that can be reused for all circular images
     */
    private void createCircleMask() {
        try {
            // Create a 128x128 circular mask image
            NativeImage maskImage = new NativeImage(128, 128, false);

            // Fill with transparent black
            for (int x = 0; x < 128; x++) {
                for (int y = 0; y < 128; y++) {
                    maskImage.setColor(x, y, 0x00000000);
                }
            }

            // Draw a white circle
            int centerX = 64;
            int centerY = 64;
            int radius = 64;
            int radiusSquared = radius * radius;

            for (int x = 0; x < 128; x++) {
                for (int y = 0; y < 128; y++) {
                    int dx = x - centerX;
                    int dy = y - centerY;
                    if (dx * dx + dy * dy <= radiusSquared) {
                        maskImage.setColor(x, y, 0xFFFFFFFF);
                    }
                }
            }

            // Create a texture from the mask
            NativeImageBackedTexture texture = new NativeImageBackedTexture(maskImage);

            // Register the texture
            CIRCLE_MASK_IDENTIFIER = MinecraftClient.getInstance().getTextureManager()
                .registerDynamicTexture("circle_mask", texture);

            com.pokecobble.Pokecobbleclaim.LOGGER.info("Created circular mask texture");
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Failed to create circular mask: " + e.getMessage());
        }
    }

    @Override
    protected void init() {
        super.init();

        // Pre-calculate panel dimensions
        panelWidth = Math.max(600, width - 100);
        panelHeight = height - 60;
        panelX = (width - panelWidth) / 2;
        panelY = 30;
        centerY = panelY + (panelHeight / 2);

        // Only request town image data if needed (throttle requests)
        if (town != null) {
            String cacheKey = town.getId().toString();
            long currentTime = System.currentTimeMillis();
            Long lastUpdateTime = LAST_IMAGE_UPDATE_TIME.get(cacheKey);

            if (lastUpdateTime == null || currentTime - lastUpdateTime > IMAGE_UPDATE_COOLDOWN_MS) {
                TownImageSynchronizer.requestTownImageUpdate(town.getId());
                LAST_IMAGE_UPDATE_TIME.put(cacheKey, currentTime);
            }
        }

        // Load available images asynchronously
        if (!imagesLoaded || needsImageRefresh) {
            CompletableFuture.runAsync(() -> {
                loadAvailableImages();
                imagesLoaded = true;
                needsImageRefresh = false;

                // Calculate maximum scroll offset
                updateMaxScrollOffset();

                // Pre-calculate rendering values
                calculateRenderingValues();
            }, IMAGE_LOADING_EXECUTOR);
        } else {
            // Just update the maximum scroll offset and rendering values
            updateMaxScrollOffset();
            calculateRenderingValues();
        }
    }

    /**
     * Pre-calculates values used for rendering to avoid recalculating every frame
     */
    private void calculateRenderingValues() {
        // Calculate how many images to display
        int imagesToDisplay = Math.min(VISIBLE_IMAGES, availableImages.size());

        // Calculate total width based on actual number of images to display
        totalWidth = imagesToDisplay * (IMAGE_SIZE + IMAGE_PADDING) - IMAGE_PADDING;
        startX = (width - totalWidth) / 2;

        // Calculate starting index for centering
        if (availableImages.size() <= imagesToDisplay) {
            // If we have fewer images than visible slots, start from the beginning
            startIndex = 0;
            scrollOffset = 0; // Reset scroll offset
        } else {
            // Otherwise, use the scroll offset
            startIndex = scrollOffset;
        }
    }

    /**
     * Updates the maximum scroll offset based on the number of available images.
     */
    private void updateMaxScrollOffset() {
        if (availableImages.size() <= VISIBLE_IMAGES) {
            maxScrollOffset = 0;
        } else {
            maxScrollOffset = availableImages.size() - VISIBLE_IMAGES;
        }

        // Ensure scroll offset is within bounds
        if (scrollOffset > maxScrollOffset) {
            scrollOffset = maxScrollOffset;
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context);

        // If images are still loading, show a loading message
        if (!imagesLoaded) {
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("Loading images...").setStyle(Style.EMPTY.withBold(true)),
                    this.width / 2, this.height / 2, 0xFFFFFF);
            super.render(context, mouseX, mouseY, delta);
            return;
        }

        // Draw panel background
        context.fill(panelX, panelY, panelX + panelWidth, panelY + panelHeight, BACKGROUND_COLOR);

        // Draw panel border
        context.fill(panelX, panelY, panelX + panelWidth, panelY + 2, BORDER_COLOR); // Top
        context.fill(panelX, panelY + panelHeight - 2, panelX + panelWidth, panelY + panelHeight, BORDER_COLOR); // Bottom
        context.fill(panelX, panelY, panelX + 2, panelY + panelHeight, BORDER_COLOR); // Left
        context.fill(panelX + panelWidth - 2, panelY, panelX + panelWidth, panelY + panelHeight, BORDER_COLOR); // Right

        // Draw header
        context.fill(panelX, panelY, panelX + panelWidth, panelY + 30, HEADER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Town Image Selection").setStyle(Style.EMPTY.withBold(true)),
                this.width / 2, panelY + 10, 0xFFFFFF);

        // Draw instructions
        if (availableImages.isEmpty()) {
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("Click the + button to upload an image for your town").setStyle(Style.EMPTY.withColor(Formatting.GRAY)),
                    this.width / 2, panelY + 40, 0xAAAAAA);
        } else {
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("Click an image to select and edit it • Scroll to see more").setStyle(Style.EMPTY.withColor(Formatting.GRAY)),
                    this.width / 2, panelY + 40, 0xAAAAAA);
        }

        // If no images available, show upload button
        if (availableImages.isEmpty()) {
            // Draw upload button (+ icon in circle)
            int uploadButtonX = this.width / 2 - (IMAGE_SIZE / 2);
            int uploadButtonY = centerY - (IMAGE_SIZE / 2);

            // Draw circular background
            int uploadCenterX = uploadButtonX + IMAGE_SIZE / 2;
            int uploadCenterY = uploadButtonY + IMAGE_SIZE / 2;
            int uploadRadius = IMAGE_SIZE / 2;

            // Draw circle border
            drawCircle(context, uploadCenterX, uploadCenterY, uploadRadius, 0xFF888888);

            // Draw + icon
            String plusIcon = "+";
            int textWidth = this.textRenderer.getWidth(plusIcon);
            int textHeight = this.textRenderer.fontHeight;
            context.drawTextWithShadow(this.textRenderer, plusIcon,
                uploadCenterX - textWidth / 2, uploadCenterY - textHeight / 2, 0xFFFFFF);

            return; // Don't draw regular image grid
        }

        // Calculate how many images to display
        int imagesToDisplay = Math.min(VISIBLE_IMAGES, availableImages.size());

        // Draw scroll indicators if needed
        if (scrollOffset > 0) {
            // Left arrow
            String leftArrow = "◀";
            context.drawTextWithShadow(this.textRenderer, leftArrow, startX - 20, centerY - 5, 0xFFFFFF);
        }

        if (scrollOffset < maxScrollOffset) {
            // Right arrow
            String rightArrow = "▶";
            context.drawTextWithShadow(this.textRenderer, rightArrow, startX + totalWidth + 10, centerY - 5, 0xFFFFFF);
        }

        // Draw images in a horizontal row
        for (int i = 0; i < imagesToDisplay && i + startIndex < availableImages.size(); i++) {
            int x = startX + i * (IMAGE_SIZE + IMAGE_PADDING);
            int y = centerY - (IMAGE_SIZE / 2);

            // Get image identifier
            String imageName = availableImages.get(i + startIndex);
            Identifier imageId = TownImageUtil.getImageIdentifier(town, imageName);

            // Draw image background (circular)
            boolean isSelected = (i + startIndex) == selectedImageIndex;
            int bgColor = isSelected ? SELECTED_COLOR : UNSELECTED_COLOR;

            // Calculate center and radius of the circular image
            int imageCenterX = x + IMAGE_SIZE / 2;
            int imageCenterY = y + IMAGE_SIZE / 2;
            int imageRadius = IMAGE_SIZE / 2;

            // Draw circular background (using a more efficient method)
            drawEfficientCircle(context, imageCenterX, imageCenterY, imageRadius + 2, bgColor);

            // Draw image if available
            if (imageId != null) {
                try {
                    // Get image settings if available
                    TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);

                    // Draw the image with circular masking
                    if (settings != null) {
                        // Apply image transformations
                        int scaledSize = (int)(IMAGE_SIZE * settings.scale);
                        int imageX = x + (IMAGE_SIZE - scaledSize) / 2 + settings.offsetX;
                        int imageY = y + (IMAGE_SIZE - scaledSize) / 2 + settings.offsetY;

                        // Draw the image with efficient circular masking
                        drawEfficientCircularImage(context, imageId, imageX, imageY, scaledSize, scaledSize,
                                          imageCenterX, imageCenterY, imageRadius);
                    } else {
                        // Draw without transformations but centered
                        drawEfficientCircularImage(context, imageId, x, y, IMAGE_SIZE, IMAGE_SIZE,
                                          imageCenterX, imageCenterY, imageRadius);
                    }

                    // Draw circle border on top to ensure clean edges
                    drawEfficientCircleOutline(context, imageCenterX, imageCenterY, imageRadius, bgColor);
                } catch (Exception e) {
                    // If there's an error drawing the texture, show a placeholder
                    drawPlaceholder(context, x, y, imageName);
                }
            } else {
                // Draw a placeholder if no image is available
                drawPlaceholder(context, x, y, imageName);
            }

            // Draw image name
            String displayName = imageName.length() > 12 ? imageName.substring(0, 10) + "..." : imageName;
            context.drawCenteredTextWithShadow(this.textRenderer, displayName, x + IMAGE_SIZE / 2, y + IMAGE_SIZE + 15, 0xFFFFFF);
        }

        // Draw 'ESC to exit' at the bottom
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Press ESC to exit").setStyle(Style.EMPTY.withColor(Formatting.GRAY)),
                this.width / 2, panelY + panelHeight - 20, 0xAAAAAA);

        // Draw scroll position indicator
        if (availableImages.size() > VISIBLE_IMAGES) {
            int indicatorWidth = panelWidth - 40;
            int indicatorX = panelX + 20;
            int indicatorY = panelY + panelHeight - 40;

            // Draw background track
            context.fill(indicatorX, indicatorY, indicatorX + indicatorWidth, indicatorY + 4, 0x55FFFFFF);

            // Draw position indicator
            float progress = (float) scrollOffset / maxScrollOffset;
            int handleWidth = Math.max(40, indicatorWidth / maxScrollOffset);
            int handleX = indicatorX + (int)((indicatorWidth - handleWidth) * progress);
            context.fill(handleX, indicatorY, handleX + handleWidth, indicatorY + 4, 0xFFFFFFFF);
        }

        super.render(context, mouseX, mouseY, delta);
    }

    /**
     * Draws a filled circle using a more efficient algorithm.
     * Uses horizontal lines to fill the circle instead of checking each pixel.
     */
    private void drawEfficientCircle(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Draw a filled circle using horizontal lines
        for (int y = -radius; y <= radius; y++) {
            int x = (int) Math.sqrt(radius * radius - y * y);
            context.fill(centerX - x, centerY + y, centerX + x + 1, centerY + y + 1, color);
        }
    }



    /**
     * Draws a circle outline using the midpoint circle algorithm.
     * Much more efficient than the previous implementation.
     */
    private void drawEfficientCircleOutline(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Use the midpoint circle algorithm
        int x = radius;
        int y = 0;
        int err = 0;

        while (x >= y) {
            // Draw 8 points for each step to complete the circle
            context.fill(centerX + x, centerY + y, centerX + x + 1, centerY + y + 1, color);
            context.fill(centerX + y, centerY + x, centerX + y + 1, centerY + x + 1, color);
            context.fill(centerX - y, centerY + x, centerX - y + 1, centerY + x + 1, color);
            context.fill(centerX - x, centerY + y, centerX - x + 1, centerY + y + 1, color);
            context.fill(centerX - x, centerY - y, centerX - x + 1, centerY - y + 1, color);
            context.fill(centerX - y, centerY - x, centerX - y + 1, centerY - x + 1, color);
            context.fill(centerX + y, centerY - x, centerX + y + 1, centerY - x + 1, color);
            context.fill(centerX + x, centerY - y, centerX + x + 1, centerY - y + 1, color);

            y += 1;
            if (err <= 0) {
                err += 2 * y + 1;
            }
            if (err > 0) {
                x -= 1;
                err -= 2 * x + 1;
            }
        }
    }

    /**
     * Draws an image with proper circular clipping and aspect ratio preservation.
     * Uses pixel-by-pixel rendering to ensure the image is only visible within the circle.
     */
    private void drawEfficientCircularImage(DrawContext context, Identifier imageId, int x, int y, int width, int height,
                                  int circleCenterX, int circleCenterY, int circleRadius) {
        try {
            // Calculate the bounding box of the circle
            int left = circleCenterX - circleRadius;
            int top = circleCenterY - circleRadius;
            int right = circleCenterX + circleRadius;
            int bottom = circleCenterY + circleRadius;

            // Pre-calculate radius squared for efficiency
            int radiusSquared = circleRadius * circleRadius;

            // Draw the image pixel by pixel, only within the circle
            for (int py = top; py < bottom; py++) {
                for (int px = left; px < right; px++) {
                    int dx = px - circleCenterX;
                    int dy = py - circleCenterY;
                    if (dx * dx + dy * dy <= radiusSquared) {
                        // This point is inside the circle, calculate the corresponding image pixel
                        int imgX = px - x;
                        int imgY = py - y;

                        // Only draw if the pixel is within the image bounds
                        if (imgX >= 0 && imgX < width && imgY >= 0 && imgY < height) {
                            // Draw a single pixel from the image
                            context.drawTexture(imageId,
                                              px, py, // destination position
                                              imgX, imgY, // source position in texture
                                              1, 1, // size to draw (1x1 pixel)
                                              width, height); // texture dimensions
                        }
                    }
                }
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error drawing circular image: " + e.getMessage());
            // Fallback to simple texture drawing
            context.drawTexture(imageId, x, y, 0, 0, width, height, width, height);
        }
    }

    /**
     * Draws a placeholder for missing images.
     * Optimized to use fewer fill operations.
     */
    private void drawPlaceholder(DrawContext context, int x, int y, String imageName) {
        // Fill with a gray background
        context.fill(x, y, x + IMAGE_SIZE, y + IMAGE_SIZE, 0xFF666666);

        // Draw a simplified pattern to indicate missing texture
        // Use larger blocks to reduce the number of fill operations
        for (int i = 0; i < IMAGE_SIZE; i += 32) {
            for (int j = 0; j < IMAGE_SIZE; j += 32) {
                boolean isAlternate = ((i / 32) + (j / 32)) % 2 == 0;
                context.fill(x + i, y + j, x + i + 16, y + j + 16, isAlternate ? 0xFFFF00FF : 0xFF000000);
                context.fill(x + i + 16, y + j, x + i + 32, y + j + 16, isAlternate ? 0xFF000000 : 0xFFFF00FF);
                context.fill(x + i, y + j + 16, x + i + 16, y + j + 32, isAlternate ? 0xFF000000 : 0xFFFF00FF);
                context.fill(x + i + 16, y + j + 16, x + i + 32, y + j + 32, isAlternate ? 0xFFFF00FF : 0xFF000000);
            }
        }

        // Draw a question mark in the center
        String noImg = "?";
        context.drawCenteredTextWithShadow(this.textRenderer, noImg, x + IMAGE_SIZE / 2, y + IMAGE_SIZE / 2 - 4, 0xFFFFFF);

        // Draw the image name at the bottom
        String shortName = imageName.length() > 10 ? imageName.substring(0, 7) + "..." : imageName;
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Missing: " + shortName).setStyle(Style.EMPTY.withColor(Formatting.RED)),
                x + IMAGE_SIZE / 2, y + IMAGE_SIZE - 15, 0xFFFFFF);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // If images are still loading, ignore clicks
        if (!imagesLoaded) {
            return super.mouseClicked(mouseX, mouseY, button);
        }

        // Check if clicked on an image
        if (button == 0) { // Left click
            // If no images available, check if clicked on upload button
            if (availableImages.isEmpty()) {
                int uploadButtonX = this.width / 2 - (IMAGE_SIZE / 2);
                int uploadButtonY = centerY - (IMAGE_SIZE / 2);
                int uploadCenterX = uploadButtonX + IMAGE_SIZE / 2;
                int uploadCenterY = uploadButtonY + IMAGE_SIZE / 2;
                int uploadRadius = IMAGE_SIZE / 2;

                // Check if click is within the upload circle
                double distance = Math.sqrt(Math.pow(mouseX - uploadCenterX, 2) + Math.pow(mouseY - uploadCenterY, 2));
                if (distance <= uploadRadius) {
                    openFileUploadDialog();
                    playClickSound();
                    return true;
                }
                return false;
            }

            // Calculate how many images to display
            int imagesToDisplay = Math.min(VISIBLE_IMAGES, availableImages.size());

            // Check if clicked on left arrow
            if (scrollOffset > 0 && mouseX >= startX - 25 && mouseX <= startX - 10 &&
                mouseY >= centerY - 10 && mouseY <= centerY + 10) {
                scrollOffset--;
                calculateRenderingValues(); // Update rendering values
                playClickSound();
                return true;
            }

            // Check if clicked on right arrow
            if (scrollOffset < maxScrollOffset && mouseX >= startX + totalWidth + 10 && mouseX <= startX + totalWidth + 25 &&
                mouseY >= centerY - 10 && mouseY <= centerY + 10) {
                scrollOffset++;
                calculateRenderingValues(); // Update rendering values
                playClickSound();
                return true;
            }

            // Check if clicked on an image
            for (int i = 0; i < imagesToDisplay && i + startIndex < availableImages.size(); i++) {
                int x = startX + i * (IMAGE_SIZE + IMAGE_PADDING);
                int y = centerY - (IMAGE_SIZE / 2);

                // Check if click is inside the circular image area
                int imageCenterX = x + IMAGE_SIZE / 2;
                int imageCenterY = y + IMAGE_SIZE / 2;
                int radius = IMAGE_SIZE / 2;

                if (isPointInCircle((int)mouseX, (int)mouseY, imageCenterX, imageCenterY, radius)) {
                    int clickedIndex = i + startIndex;

                    // Check if this image is already selected
                    if (clickedIndex == selectedImageIndex) {
                        // Already selected, open the editor
                        String imageName = availableImages.get(selectedImageIndex);
                        playClickSound();

                        // Check if town is valid before opening the editor
                        if (town == null) {
                            com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot open image editor: town is null");
                            // Show an error message to the user
                            if (this.client.player != null) {
                                this.client.player.sendMessage(Text.literal("Error: Could not open image editor. Town data is missing.").setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                            }
                            return true;
                        }

                        // Open the image editor screen
                        this.client.setScreen(new ImageEditorScreen(this, town, imageName));
                    } else {
                        // Not selected yet, just select it
                        selectedImageIndex = clickedIndex;

                        // Get the selected image name
                        String imageName = availableImages.get(selectedImageIndex);

                        // Save the selected image
                        saveTownImage(imageName);

                        playClickSound();
                    }
                    return true;
                }
            }

            // Check if clicked on scroll bar
            if (availableImages.size() > VISIBLE_IMAGES) {
                int indicatorWidth = panelWidth - 40;
                int indicatorX = panelX + 20;
                int indicatorY = panelY + panelHeight - 40;

                if (mouseY >= indicatorY && mouseY <= indicatorY + 4 &&
                    mouseX >= indicatorX && mouseX <= indicatorX + indicatorWidth) {
                    // Calculate new scroll position based on click position
                    float clickPosition = (float)(mouseX - indicatorX) / indicatorWidth;
                    scrollOffset = Math.round(clickPosition * maxScrollOffset);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScrollOffset));
                    calculateRenderingValues(); // Update rendering values
                    playClickSound();
                    return true;
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Checks if a point is inside a circle.
     */
    private boolean isPointInCircle(int pointX, int pointY, int circleX, int circleY, int radius) {
        int dx = pointX - circleX;
        int dy = pointY - circleY;
        return dx * dx + dy * dy <= radius * radius;
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // If images are still loading, ignore scrolling
        if (!imagesLoaded) {
            return super.mouseScrolled(mouseX, mouseY, amount);
        }

        if (availableImages.size() > VISIBLE_IMAGES) {
            if (amount > 0) { // Scroll up/left
                scrollOffset = Math.max(0, scrollOffset - 1);
                calculateRenderingValues(); // Update rendering values
                return true;
            } else if (amount < 0) { // Scroll down/right
                scrollOffset = Math.min(maxScrollOffset, scrollOffset + 1);
                calculateRenderingValues(); // Update rendering values
                return true;
            }
        }
        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public void close() {
        // If an image is selected, send comprehensive update to server
        if (selectedImageIndex >= 0 && selectedImageIndex < availableImages.size() && town != null) {
            String imageName = availableImages.get(selectedImageIndex);

            // Get current image settings (or use defaults)
            TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);
            float scale = settings != null ? settings.scale : 1.0f;
            int offsetX = settings != null ? settings.offsetX : 0;
            int offsetY = settings != null ? settings.offsetY : 0;

            // Send comprehensive image selection update to server
            sendImageSelectionToServer(town.getId(), imageName, scale, offsetX, offsetY);

            // Apply changes locally for immediate feedback
            town.setImage(imageName);
            TownImageUtil.applyImageSettingsLocally(town, imageName, scale, offsetX, offsetY);
            town.markChanged(Town.ASPECT_IMAGE);
        }

        this.client.setScreen(parent);
    }

    /**
     * Sends the complete image selection data to the server.
     */
    private void sendImageSelectionToServer(UUID townId, String imageName, float scale, int offsetX, int offsetY) {
        try {
            // Create packet buffer
            net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();

            // Write town ID
            buf.writeUuid(townId);

            // Write image name
            buf.writeString(imageName, com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);

            // Write image settings
            buf.writeFloat(scale);
            buf.writeInt(offsetX);
            buf.writeInt(offsetY);

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.town.network.NetworkConstants.TOWN_IMAGE_SELECTION_UPDATE, buf);

            Pokecobbleclaim.LOGGER.info("Sent image selection update to server: " + imageName + " for town " + townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send image selection to server: " + e.getMessage());

            // Fallback to old synchronization method
            try {
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("image", imageName);
                java.util.Map<String, Object> imageSettingsMap = new java.util.HashMap<>();
                imageSettingsMap.put("scale", scale);
                imageSettingsMap.put("offsetX", offsetX);
                imageSettingsMap.put("offsetY", offsetY);
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("imageSettings", imageSettingsMap);
            } catch (Exception fallbackError) {
                Pokecobbleclaim.LOGGER.error("Fallback synchronization also failed: " + fallbackError.getMessage());
            }
        }
    }

    /**
     * Loads available images for the town.
     * Optimized to handle errors gracefully and avoid blocking the main thread.
     */
    public void loadAvailableImages() {
        availableImages.clear();

        try {
            // Load town-specific images only
            if (town != null) {
                // Check if town directory exists
                Path townDir = Paths.get(TOWN_IMAGES_PATH + town.getName());
                if (Files.exists(townDir)) {
                    // List all image files in the directory
                    List<Path> imagePaths = Files.list(townDir)
                        .filter(path -> {
                            String fileName = path.getFileName().toString().toLowerCase();
                            return fileName.endsWith(".png") || fileName.endsWith(".jpg") || fileName.endsWith(".jpeg");
                        })
                        .toList(); // Collect to a list to avoid stream issues

                    // Process the collected paths
                    for (Path path : imagePaths) {
                        String fileName = path.getFileName().toString();
                        // Remove extension
                        fileName = fileName.substring(0, fileName.lastIndexOf('.'));
                        availableImages.add(fileName);
                    }
                }

                // Check if town has a selected image
                String currentImage = town.getImage();
                if (currentImage != null && !currentImage.isEmpty()) {
                    for (int i = 0; i < availableImages.size(); i++) {
                        if (availableImages.get(i).equals(currentImage)) {
                            selectedImageIndex = i;
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error loading town images: " + e.getMessage());
        }
    }

    /**
     * Saves the selected image for the town.
     */
    private void saveTownImage(String imageName) {
        if (town != null) {
            try {
                // Set the image name in the town object locally
                town.setImage(imageName);

                // Use the proper client-side synchronization method
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("image", imageName);

                // Also save default image settings
                java.util.Map<String, Object> imageSettingsMap = new java.util.HashMap<>();
                imageSettingsMap.put("scale", 1.0f);
                imageSettingsMap.put("offsetX", 0);
                imageSettingsMap.put("offsetY", 0);
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("imageSettings", imageSettingsMap);

                // Log the change
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Saved town image: " + imageName + " for town: " + town.getName() + " (syncing to server)");
            } catch (Exception e) {
                // Log the error
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Error saving town image: " + e.getMessage(), e);
                // Show an error message to the user
                if (this.client != null && this.client.player != null) {
                    this.client.player.sendMessage(Text.literal("Error: Could not save town image. " + e.getMessage()).setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                }
            }
        } else {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot save town image: town is null");
            // Show an error message to the user
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(Text.literal("Error: Could not save town image. Town data is missing.").setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
            }
        }
    }

    /**
     * Draws a circle outline.
     */
    private void drawCircle(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Draw a simple circle using multiple small rectangles
        for (int angle = 0; angle < 360; angle += 2) {
            double radians = Math.toRadians(angle);
            int x = (int) (centerX + radius * Math.cos(radians));
            int y = (int) (centerY + radius * Math.sin(radians));
            context.fill(x, y, x + 1, y + 1, color);
        }
    }

    /**
     * Opens a file upload dialog for selecting an image.
     */
    private void openFileUploadDialog() {
        // Show instructions to the user about how to upload images
        if (this.client != null && this.client.player != null) {
            this.client.player.sendMessage(
                Text.literal("=== Town Image Upload Instructions ===")
                    .setStyle(Style.EMPTY.withColor(Formatting.GOLD)), false);
            this.client.player.sendMessage(
                Text.literal("1. Place your image file in: .minecraft/config/pokecobbleclaim/upload/")
                    .setStyle(Style.EMPTY.withColor(Formatting.YELLOW)), false);
            this.client.player.sendMessage(
                Text.literal("2. Supported formats: PNG, JPG, JPEG (max 5MB)")
                    .setStyle(Style.EMPTY.withColor(Formatting.GRAY)), false);
            this.client.player.sendMessage(
                Text.literal("3. Click the + button again to scan for new images")
                    .setStyle(Style.EMPTY.withColor(Formatting.GREEN)), false);

            // Create upload directory if it doesn't exist
            createUploadDirectory();

            // Scan for images in upload directory
            scanUploadDirectory();
        }
    }

    /**
     * Creates the upload directory if it doesn't exist.
     */
    private void createUploadDirectory() {
        try {
            Path uploadDir = Paths.get("config/pokecobbleclaim/upload");
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Created upload directory: " + uploadDir);
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error creating upload directory: " + e.getMessage());
        }
    }

    /**
     * Scans the upload directory for new images and processes them.
     */
    private void scanUploadDirectory() {
        try {
            Path uploadDir = Paths.get("config/pokecobbleclaim/upload");
            if (!Files.exists(uploadDir)) {
                return;
            }

            List<Path> imageFiles = Files.list(uploadDir)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return fileName.endsWith(".png") || fileName.endsWith(".jpg") || fileName.endsWith(".jpeg");
                })
                .toList();

            if (imageFiles.isEmpty()) {
                if (this.client != null && this.client.player != null) {
                    this.client.player.sendMessage(
                        Text.literal("No images found in upload directory.")
                            .setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                }
                return;
            }

            // Process the first image found
            Path firstImage = imageFiles.get(0);
            java.io.File imageFile = firstImage.toFile();

            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("Found image: " + firstImage.getFileName() + ". Processing...")
                        .setStyle(Style.EMPTY.withColor(Formatting.GREEN)), false);
            }

            processSelectedImage(imageFile);

            // Delete the processed file
            try {
                Files.delete(firstImage);
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Deleted processed upload file: " + firstImage);
            } catch (Exception e) {
                com.pokecobble.Pokecobbleclaim.LOGGER.warn("Could not delete upload file: " + e.getMessage());
            }

        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error scanning upload directory: " + e.getMessage());
        }
    }

    /**
     * Processes the selected image file.
     */
    private void processSelectedImage(java.io.File imageFile) {
        try {
            // Validate file size (max 5MB)
            long maxSize = 5 * 1024 * 1024; // 5MB
            if (imageFile.length() > maxSize) {
                if (this.client != null && this.client.player != null) {
                    this.client.player.sendMessage(
                        Text.literal("Error: Image file is too large. Maximum size is 5MB.")
                            .setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                }
                return;
            }

            // Read the image file
            byte[] imageData = java.nio.file.Files.readAllBytes(imageFile.toPath());

            // Generate a unique filename
            String fileName = "uploaded_" + System.currentTimeMillis();
            String extension = getFileExtension(imageFile.getName());

            // Send the image to the server
            sendImageToServer(fileName, extension, imageData);

        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error processing selected image: " + e.getMessage());
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("Error: Could not process selected image. " + e.getMessage())
                        .setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
            }
        }
    }

    /**
     * Gets the file extension from a filename.
     */
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot);
        }
        return ".png"; // Default extension
    }

    /**
     * Sends the image data to the server.
     */
    private void sendImageToServer(String fileName, String extension, byte[] imageData) {
        try {
            if (town == null) {
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot send image: town is null");
                return;
            }

            com.pokecobble.Pokecobbleclaim.LOGGER.info("Sending image to server: " + fileName + extension + " (" + imageData.length + " bytes)");

            // Step 1: Send upload request
            net.minecraft.network.PacketByteBuf requestBuf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            requestBuf.writeUuid(town.getId());
            requestBuf.writeString(fileName, com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            requestBuf.writeString(extension, com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            requestBuf.writeInt(imageData.length);

            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.town.network.NetworkConstants.TOWN_IMAGE_UPLOAD_REQUEST, requestBuf);

            // Step 2: Send image data in chunks
            int chunkSize = 8192; // 8KB chunks
            int totalChunks = (int) Math.ceil((double) imageData.length / chunkSize);

            for (int i = 0; i < totalChunks; i++) {
                int offset = i * chunkSize;
                int currentChunkSize = Math.min(chunkSize, imageData.length - offset);
                byte[] chunk = new byte[currentChunkSize];
                System.arraycopy(imageData, offset, chunk, 0, currentChunkSize);

                net.minecraft.network.PacketByteBuf chunkBuf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
                chunkBuf.writeInt(i); // chunk index
                chunkBuf.writeInt(currentChunkSize);
                chunkBuf.writeBytes(chunk);

                net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                    com.pokecobble.town.network.NetworkConstants.TOWN_IMAGE_UPLOAD_CHUNK, chunkBuf);
            }

            // Step 3: Send completion signal
            net.minecraft.network.PacketByteBuf completeBuf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.town.network.NetworkConstants.TOWN_IMAGE_UPLOAD_COMPLETE, completeBuf);

            // Show success message to user
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("Uploading image... Please wait.")
                        .setStyle(Style.EMPTY.withColor(Formatting.GREEN)), false);
            }

        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error sending image to server: " + e.getMessage());
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("Error: Could not upload image. " + e.getMessage())
                        .setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
            }
        }
    }

}
