package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.network.town.TownImageSynchronizer;
import com.pokecobble.town.sound.SoundUtil;
import com.pokecobble.town.util.TownImageUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.text.Text;
import net.minecraft.text.Style;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Optimized screen for selecting a town image.
 */
public class TownImageScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private List<String> availableImages = new ArrayList<>();
    private int selectedImageIndex = -1;
    // Modern Color Palette (matching MyTownScreen)
    private static final int PRIMARY_BG = 0xE8121212;        // Dark background
    private static final int SECONDARY_BG = 0xF01A1A1A;     // Slightly lighter
    private static final int CARD_BG = 0xF0252525;          // Card background
    private static final int ACCENT_PRIMARY = 0xFF4A90E2;   // Modern blue
    private static final int ACCENT_SUCCESS = 0xFF27AE60;   // Success green
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text
    private static final int BORDER_COLOR = 0xFF333333;     // Border color
    private static final int HOVER_OVERLAY = 0x20FFFFFF;    // Hover effect

    // Panel dimensions (matching MyTownScreen style)
    private int panelWidth = 480;
    private int panelHeight = 400;
    private static final int SHADOW_SIZE = 3;

    // Spacing system (4px grid)
    private static final int SPACING_XS = 2;
    private static final int SPACING_SM = 4;
    private static final int SPACING_MD = 8;
    private static final int SPACING_LG = 12;
    private static final int SPACING_XL = 16;

    // Image slot constants
    private static final int IMAGE_SIZE = 80; // Smaller, more compact
    private static final int IMAGE_PADDING = 20; // Tighter spacing
    private static final int MAX_IMAGES = 3; // Always show exactly 3 slots

    // Caching and optimization
    private static final Map<String, Long> LAST_IMAGE_UPDATE_TIME = new HashMap<>();
    private static final long IMAGE_UPDATE_COOLDOWN_MS = 5000; // 5 seconds cooldown between updates
    private boolean imagesLoaded = false;
    private boolean needsImageRefresh = true;
    private static final Executor IMAGE_LOADING_EXECUTOR = Executors.newSingleThreadExecutor();

    // Path to town images
    private static final String TOWN_IMAGES_PATH = "config/pokecobbleclaim/towns/";

    // Circular mask identifiers
    private static Identifier CIRCLE_MASK_IDENTIFIER;

    public TownImageScreen(Screen parent) {
        super(Text.translatable("screen.pokecobbleclaim.town_image"));
        this.parent = parent;

        // Get the player's town from ClientTownManager (client-side)
        this.town = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

        // Initialize the circular mask if not already created
        if (CIRCLE_MASK_IDENTIFIER == null) {
            createCircleMask();
        }
    }

    /**
     * Creates a circular mask texture that can be reused for all circular images
     */
    private void createCircleMask() {
        try {
            // Create a 128x128 circular mask image
            NativeImage maskImage = new NativeImage(128, 128, false);

            // Fill with transparent black
            for (int x = 0; x < 128; x++) {
                for (int y = 0; y < 128; y++) {
                    maskImage.setColor(x, y, 0x00000000);
                }
            }

            // Draw a white circle
            int centerX = 64;
            int centerY = 64;
            int radius = 64;
            int radiusSquared = radius * radius;

            for (int x = 0; x < 128; x++) {
                for (int y = 0; y < 128; y++) {
                    int dx = x - centerX;
                    int dy = y - centerY;
                    if (dx * dx + dy * dy <= radiusSquared) {
                        maskImage.setColor(x, y, 0xFFFFFFFF);
                    }
                }
            }

            // Create a texture from the mask
            NativeImageBackedTexture texture = new NativeImageBackedTexture(maskImage);

            // Register the texture
            CIRCLE_MASK_IDENTIFIER = MinecraftClient.getInstance().getTextureManager()
                .registerDynamicTexture("circle_mask", texture);

            com.pokecobble.Pokecobbleclaim.LOGGER.info("Created circular mask texture");
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Failed to create circular mask: " + e.getMessage());
        }
    }

    @Override
    protected void init() {
        super.init();

        // Panel dimensions are now constants (matching MyTownScreen style)
        // No need to calculate them here

        // Only request town image data if needed (throttle requests)
        if (town != null) {
            String cacheKey = town.getId().toString();
            long currentTime = System.currentTimeMillis();
            Long lastUpdateTime = LAST_IMAGE_UPDATE_TIME.get(cacheKey);

            if (lastUpdateTime == null || currentTime - lastUpdateTime > IMAGE_UPDATE_COOLDOWN_MS) {
                TownImageSynchronizer.requestTownImageUpdate(town.getId());
                LAST_IMAGE_UPDATE_TIME.put(cacheKey, currentTime);
            }
        }

        // Load available images asynchronously
        if (!imagesLoaded || needsImageRefresh) {
            CompletableFuture.runAsync(() -> {
                loadAvailableImages();
                imagesLoaded = true;
                needsImageRefresh = false;

                // Pre-calculate rendering values
                calculateRenderingValues();
            }, IMAGE_LOADING_EXECUTOR);
        } else {
            // Just update rendering values
            calculateRenderingValues();
        }
    }

    /**
     * Pre-calculates values used for rendering to avoid recalculating every frame
     */
    private void calculateRenderingValues() {
        // Values are calculated in render method now
    }



    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render modern background
        this.renderBackground(context);

        // Calculate centered panel position (matching MyTownScreen)
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // If images are still loading, show a loading message
        if (!imagesLoaded) {
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("Loading images...").setStyle(Style.EMPTY.withBold(true)),
                    this.width / 2, this.height / 2, TEXT_PRIMARY);
            super.render(context, mouseX, mouseY, delta);
            return;
        }

        // Draw modern panel with shadow effect (matching MyTownScreen)
        drawModernPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw modern header
        drawModernHeader(context, leftX, topY, panelWidth);

        // Calculate content area
        int contentX = leftX + SPACING_MD;
        int contentY = topY + SPACING_LG + 12 + SPACING_SM;
        int contentWidth = panelWidth - SPACING_MD * 2;
        int contentHeight = panelHeight - (SPACING_LG + 12 + SPACING_SM + SPACING_LG + 24);

        // Draw upload instructions
        drawUploadInstructions(context, contentX, contentY, contentWidth);

        // Draw exactly 3 image slots
        drawImageSlots(context, mouseX, mouseY, contentX, contentY + 120, contentWidth);

        // Draw exit instructions
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Press ESC to exit").setStyle(Style.EMPTY.withColor(Formatting.GRAY)),
                this.width / 2, topY + panelHeight - SPACING_LG, TEXT_MUTED);

        super.render(context, mouseX, mouseY, delta);
    }

    /**
     * Draws a compact modern panel with subtle shadow and gradient background (matching MyTownScreen)
     */
    private void drawModernPanel(DrawContext context, int x, int y, int width, int height) {
        // Draw compact shadow
        for (int i = 0; i < SHADOW_SIZE; i++) {
            int shadowAlpha = (SHADOW_SIZE - i) * 15;
            int shadowColor = shadowAlpha << 24;
            context.fill(x + i, y + i, x + width + i, y + height + i, shadowColor);
        }

        // Draw main panel background with gradient
        context.fillGradient(x, y, x + width, y + height, PRIMARY_BG, SECONDARY_BG);

        // Draw subtle border
        context.drawBorder(x, y, width, height, BORDER_COLOR);

        // Add subtle inner glow
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x15FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);
    }

    /**
     * Draws the modern header (matching MyTownScreen)
     */
    private void drawModernHeader(DrawContext context, int x, int y, int width) {
        // Draw header background
        context.fillGradient(x, y, x + width, y + 24, CARD_BG, PRIMARY_BG);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Town Image Selection").setStyle(Style.EMPTY.withBold(true)),
                x + width / 2, y + 8, TEXT_PRIMARY);
    }



    /**
     * Draws upload instructions on the screen (modern style).
     */
    private void drawUploadInstructions(DrawContext context, int x, int y, int width) {
        // Draw instruction background
        context.fill(x, y, x + width, y + 100, CARD_BG);

        // Title for instructions
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("How to Upload Images:").setStyle(Style.EMPTY.withBold(true)),
                x + width / 2, y + SPACING_MD, TEXT_PRIMARY);

        // Step-by-step instructions (compact)
        String[] instructions = {
            "1. Click the + button to select an image slot",
            "2. Place your image file in: config/pokecobbleclaim/upload/",
            "3. Rename your file to: upload.png or upload.jpg",
            "4. Click the + button again to upload the image"
        };

        for (int i = 0; i < instructions.length; i++) {
            context.drawTextWithShadow(this.textRenderer,
                    Text.literal(instructions[i]),
                    x + SPACING_MD, y + 25 + (i * 12), TEXT_SECONDARY);
        }

        // Additional info
        context.drawTextWithShadow(this.textRenderer,
                Text.literal("• Supported: PNG, JPG, JPEG • Max size: 5MB"),
                x + SPACING_MD, y + 80, TEXT_MUTED);
    }

    /**
     * Draws exactly 3 image slots (filled or empty with + icons).
     */
    private void drawImageSlots(DrawContext context, int mouseX, int mouseY, int x, int y, int width) {
        // Calculate slot positioning
        int totalSlotsWidth = MAX_IMAGES * IMAGE_SIZE + (MAX_IMAGES - 1) * IMAGE_PADDING;
        int startX = x + (width - totalSlotsWidth) / 2;
        int slotY = y;

        for (int i = 0; i < MAX_IMAGES; i++) {
            int slotX = startX + i * (IMAGE_SIZE + IMAGE_PADDING);
            int slotCenterX = slotX + IMAGE_SIZE / 2;
            int slotCenterY = slotY + IMAGE_SIZE / 2;
            int slotRadius = IMAGE_SIZE / 2;

            boolean hasImage = i < availableImages.size();
            boolean isSelected = i == selectedImageIndex;
            boolean isHovered = mouseX >= slotX && mouseX <= slotX + IMAGE_SIZE &&
                              mouseY >= slotY && mouseY <= slotY + IMAGE_SIZE;

            // Determine colors (using modern palette)
            int bgColor;
            if (isSelected) {
                bgColor = ACCENT_SUCCESS;
            } else if (isHovered) {
                bgColor = ACCENT_PRIMARY; // Modern blue hover
            } else {
                bgColor = hasImage ? CARD_BG : SECONDARY_BG; // Modern grays
            }

            // Draw circular background
            drawEfficientCircle(context, slotCenterX, slotCenterY, slotRadius + 2, bgColor);

            if (hasImage) {
                // Draw the image
                String imageName = availableImages.get(i);
                Identifier imageId = TownImageUtil.getImageIdentifier(town, imageName);

                if (imageId != null) {
                    try {
                        TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);

                        if (settings != null) {
                            // Apply image transformations
                            int scaledSize = (int)(IMAGE_SIZE * settings.scale);
                            int imageX = slotX + (IMAGE_SIZE - scaledSize) / 2 + settings.offsetX;
                            int imageY = slotY + (IMAGE_SIZE - scaledSize) / 2 + settings.offsetY;

                            drawEfficientCircularImage(context, imageId, imageX, imageY, scaledSize, scaledSize,
                                              slotCenterX, slotCenterY, slotRadius);
                        } else {
                            drawEfficientCircularImage(context, imageId, slotX, slotY, IMAGE_SIZE, IMAGE_SIZE,
                                              slotCenterX, slotCenterY, slotRadius);
                        }

                        // Draw circle border
                        drawEfficientCircleOutline(context, slotCenterX, slotCenterY, slotRadius, bgColor);
                    } catch (Exception e) {
                        // If there's an error drawing the texture, show a placeholder
                        drawPlaceholder(context, slotX, slotY, imageName);
                    }
                } else {
                    // Draw a placeholder if no image is available
                    drawPlaceholder(context, slotX, slotY, imageName);
                }
            } else {
                // Draw + icon for empty slots
                String plusIcon = "+";
                int textWidth = this.textRenderer.getWidth(plusIcon);
                int textHeight = this.textRenderer.fontHeight;
                context.drawTextWithShadow(this.textRenderer, plusIcon,
                    slotCenterX - textWidth / 2, slotCenterY - textHeight / 2, 0xFFFFFF);
            }

            // Draw slot number below each circle
            String slotLabel = "Slot " + (i + 1);
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal(slotLabel).setStyle(Style.EMPTY.withColor(Formatting.GRAY)),
                    slotCenterX, slotY + IMAGE_SIZE + 10, 0xAAAAAA);
        }
    }

    /**
     * Draws a filled circle using a more efficient algorithm.
     * Uses horizontal lines to fill the circle instead of checking each pixel.
     */
    private void drawEfficientCircle(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Draw a filled circle using horizontal lines
        for (int y = -radius; y <= radius; y++) {
            int x = (int) Math.sqrt(radius * radius - y * y);
            context.fill(centerX - x, centerY + y, centerX + x + 1, centerY + y + 1, color);
        }
    }



    /**
     * Draws a circle outline using the midpoint circle algorithm.
     * Much more efficient than the previous implementation.
     */
    private void drawEfficientCircleOutline(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Use the midpoint circle algorithm
        int x = radius;
        int y = 0;
        int err = 0;

        while (x >= y) {
            // Draw 8 points for each step to complete the circle
            context.fill(centerX + x, centerY + y, centerX + x + 1, centerY + y + 1, color);
            context.fill(centerX + y, centerY + x, centerX + y + 1, centerY + x + 1, color);
            context.fill(centerX - y, centerY + x, centerX - y + 1, centerY + x + 1, color);
            context.fill(centerX - x, centerY + y, centerX - x + 1, centerY + y + 1, color);
            context.fill(centerX - x, centerY - y, centerX - x + 1, centerY - y + 1, color);
            context.fill(centerX - y, centerY - x, centerX - y + 1, centerY - x + 1, color);
            context.fill(centerX + y, centerY - x, centerX + y + 1, centerY - x + 1, color);
            context.fill(centerX + x, centerY - y, centerX + x + 1, centerY - y + 1, color);

            y += 1;
            if (err <= 0) {
                err += 2 * y + 1;
            }
            if (err > 0) {
                x -= 1;
                err -= 2 * x + 1;
            }
        }
    }

    /**
     * Draws an image with proper circular clipping and aspect ratio preservation.
     * Uses pixel-by-pixel rendering to ensure the image is only visible within the circle.
     */
    private void drawEfficientCircularImage(DrawContext context, Identifier imageId, int x, int y, int width, int height,
                                  int circleCenterX, int circleCenterY, int circleRadius) {
        try {
            // Calculate the bounding box of the circle
            int left = circleCenterX - circleRadius;
            int top = circleCenterY - circleRadius;
            int right = circleCenterX + circleRadius;
            int bottom = circleCenterY + circleRadius;

            // Pre-calculate radius squared for efficiency
            int radiusSquared = circleRadius * circleRadius;

            // Draw the image pixel by pixel, only within the circle
            for (int py = top; py < bottom; py++) {
                for (int px = left; px < right; px++) {
                    int dx = px - circleCenterX;
                    int dy = py - circleCenterY;
                    if (dx * dx + dy * dy <= radiusSquared) {
                        // This point is inside the circle, calculate the corresponding image pixel
                        int imgX = px - x;
                        int imgY = py - y;

                        // Only draw if the pixel is within the image bounds
                        if (imgX >= 0 && imgX < width && imgY >= 0 && imgY < height) {
                            // Draw a single pixel from the image
                            context.drawTexture(imageId,
                                              px, py, // destination position
                                              imgX, imgY, // source position in texture
                                              1, 1, // size to draw (1x1 pixel)
                                              width, height); // texture dimensions
                        }
                    }
                }
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error drawing circular image: " + e.getMessage());
            // Fallback to simple texture drawing
            context.drawTexture(imageId, x, y, 0, 0, width, height, width, height);
        }
    }

    /**
     * Draws a placeholder for missing images.
     * Optimized to use fewer fill operations.
     */
    private void drawPlaceholder(DrawContext context, int x, int y, String imageName) {
        // Fill with a gray background
        context.fill(x, y, x + IMAGE_SIZE, y + IMAGE_SIZE, 0xFF666666);

        // Draw a simplified pattern to indicate missing texture
        // Use larger blocks to reduce the number of fill operations
        for (int i = 0; i < IMAGE_SIZE; i += 32) {
            for (int j = 0; j < IMAGE_SIZE; j += 32) {
                boolean isAlternate = ((i / 32) + (j / 32)) % 2 == 0;
                context.fill(x + i, y + j, x + i + 16, y + j + 16, isAlternate ? 0xFFFF00FF : 0xFF000000);
                context.fill(x + i + 16, y + j, x + i + 32, y + j + 16, isAlternate ? 0xFF000000 : 0xFFFF00FF);
                context.fill(x + i, y + j + 16, x + i + 16, y + j + 32, isAlternate ? 0xFF000000 : 0xFFFF00FF);
                context.fill(x + i + 16, y + j + 16, x + i + 32, y + j + 32, isAlternate ? 0xFFFF00FF : 0xFF000000);
            }
        }

        // Draw a question mark in the center
        String noImg = "?";
        context.drawCenteredTextWithShadow(this.textRenderer, noImg, x + IMAGE_SIZE / 2, y + IMAGE_SIZE / 2 - 4, 0xFFFFFF);

        // Draw the image name at the bottom
        String shortName = imageName.length() > 10 ? imageName.substring(0, 7) + "..." : imageName;
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Missing: " + shortName).setStyle(Style.EMPTY.withColor(Formatting.RED)),
                x + IMAGE_SIZE / 2, y + IMAGE_SIZE - 15, 0xFFFFFF);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // If images are still loading, ignore clicks
        if (!imagesLoaded) {
            return super.mouseClicked(mouseX, mouseY, button);
        }

        // Check if clicked on a slot
        if (button == 0) { // Left click
            // Calculate layout (matching render method)
            int leftX = (width - panelWidth) / 2;
            int topY = (height - panelHeight) / 2;
            int contentX = leftX + SPACING_MD;
            int contentY = topY + SPACING_LG + 12 + SPACING_SM;
            int contentWidth = panelWidth - SPACING_MD * 2;

            // Calculate slot positioning
            int totalSlotsWidth = MAX_IMAGES * IMAGE_SIZE + (MAX_IMAGES - 1) * IMAGE_PADDING;
            int startX = contentX + (contentWidth - totalSlotsWidth) / 2;
            int slotY = contentY + 120; // Same as in render method

            for (int i = 0; i < MAX_IMAGES; i++) {
                int slotX = startX + i * (IMAGE_SIZE + IMAGE_PADDING);
                int slotCenterX = slotX + IMAGE_SIZE / 2;
                int slotCenterY = slotY + IMAGE_SIZE / 2;
                int slotRadius = IMAGE_SIZE / 2;

                // Check if click is within this slot's circle
                double distance = Math.sqrt(Math.pow(mouseX - slotCenterX, 2) + Math.pow(mouseY - slotCenterY, 2));
                if (distance <= slotRadius) {
                    boolean hasImage = i < availableImages.size();

                    if (hasImage) {
                        // Select this image for editing
                        selectedImageIndex = i;
                        String imageName = availableImages.get(i);
                        openImageEditingScreen(imageName);
                    } else {
                        // Open upload dialog for empty slot
                        openFileUploadDialog();
                    }

                    playClickSound();
                    return true;
                }
            }

        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    /**
     * Opens the image editing screen for the selected image.
     */
    private void openImageEditingScreen(String imageName) {
        // Check if town is valid before opening the editor
        if (town == null) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot open image editor: town is null");
            // Show an error message to the user
            if (this.client.player != null) {
                this.client.player.sendMessage(Text.literal("Error: Could not open image editor. Town data is missing.").setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
            }
            return;
        }

        // Save the selected image first
        saveTownImage(imageName);

        // Open the image editor screen
        this.client.setScreen(new ImageEditorScreen(this, town, imageName));
    }

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Checks if a point is inside a circle.
     */
    private boolean isPointInCircle(int pointX, int pointY, int circleX, int circleY, int radius) {
        int dx = pointX - circleX;
        int dy = pointY - circleY;
        return dx * dx + dy * dy <= radius * radius;
    }



    @Override
    public void close() {
        // If an image is selected, send comprehensive update to server
        if (selectedImageIndex >= 0 && selectedImageIndex < availableImages.size() && town != null) {
            String imageName = availableImages.get(selectedImageIndex);

            // Get current image settings (or use defaults)
            TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);
            float scale = settings != null ? settings.scale : 1.0f;
            int offsetX = settings != null ? settings.offsetX : 0;
            int offsetY = settings != null ? settings.offsetY : 0;

            // Send comprehensive image selection update to server
            sendImageSelectionToServer(town.getId(), imageName, scale, offsetX, offsetY);

            // Apply changes locally for immediate feedback
            town.setImage(imageName);
            TownImageUtil.applyImageSettingsLocally(town, imageName, scale, offsetX, offsetY);
            town.markChanged(Town.ASPECT_IMAGE);
        }

        this.client.setScreen(parent);
    }

    /**
     * Sends the complete image selection data to the server.
     */
    private void sendImageSelectionToServer(UUID townId, String imageName, float scale, int offsetX, int offsetY) {
        try {
            // Create packet buffer
            net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();

            // Write town ID
            buf.writeUuid(townId);

            // Write image name
            buf.writeString(imageName, com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);

            // Write image settings
            buf.writeFloat(scale);
            buf.writeInt(offsetX);
            buf.writeInt(offsetY);

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.town.network.NetworkConstants.TOWN_IMAGE_SELECTION_UPDATE, buf);

            Pokecobbleclaim.LOGGER.info("Sent image selection update to server: " + imageName + " for town " + townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send image selection to server: " + e.getMessage());

            // Fallback to old synchronization method
            try {
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("image", imageName);
                java.util.Map<String, Object> imageSettingsMap = new java.util.HashMap<>();
                imageSettingsMap.put("scale", scale);
                imageSettingsMap.put("offsetX", offsetX);
                imageSettingsMap.put("offsetY", offsetY);
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("imageSettings", imageSettingsMap);
            } catch (Exception fallbackError) {
                Pokecobbleclaim.LOGGER.error("Fallback synchronization also failed: " + fallbackError.getMessage());
            }
        }
    }

    /**
     * Loads available images for the town.
     * Optimized to handle errors gracefully and avoid blocking the main thread.
     */
    public void loadAvailableImages() {
        availableImages.clear();

        try {
            // Load town-specific images only
            if (town != null) {
                // Check if town directory exists
                Path townDir = Paths.get(TOWN_IMAGES_PATH + town.getName());
                if (Files.exists(townDir)) {
                    // List all image files in the directory
                    List<Path> imagePaths = Files.list(townDir)
                        .filter(path -> {
                            String fileName = path.getFileName().toString().toLowerCase();
                            return fileName.endsWith(".png") || fileName.endsWith(".jpg") || fileName.endsWith(".jpeg");
                        })
                        .toList(); // Collect to a list to avoid stream issues

                    // Process the collected paths
                    for (Path path : imagePaths) {
                        String fileName = path.getFileName().toString();
                        // Remove extension
                        fileName = fileName.substring(0, fileName.lastIndexOf('.'));
                        availableImages.add(fileName);
                    }
                }

                // Check if town has a selected image
                String currentImage = town.getImage();
                if (currentImage != null && !currentImage.isEmpty()) {
                    for (int i = 0; i < availableImages.size(); i++) {
                        if (availableImages.get(i).equals(currentImage)) {
                            selectedImageIndex = i;
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error loading town images: " + e.getMessage());
        }
    }

    /**
     * Saves the selected image for the town.
     */
    private void saveTownImage(String imageName) {
        if (town != null) {
            try {
                // Set the image name in the town object locally
                town.setImage(imageName);

                // Use the proper client-side synchronization method
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("image", imageName);

                // Also save default image settings
                java.util.Map<String, Object> imageSettingsMap = new java.util.HashMap<>();
                imageSettingsMap.put("scale", 1.0f);
                imageSettingsMap.put("offsetX", 0);
                imageSettingsMap.put("offsetY", 0);
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("imageSettings", imageSettingsMap);

                // Log the change
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Saved town image: " + imageName + " for town: " + town.getName() + " (syncing to server)");
            } catch (Exception e) {
                // Log the error
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Error saving town image: " + e.getMessage(), e);
                // Show an error message to the user
                if (this.client != null && this.client.player != null) {
                    this.client.player.sendMessage(Text.literal("Error: Could not save town image. " + e.getMessage()).setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                }
            }
        } else {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot save town image: town is null");
            // Show an error message to the user
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(Text.literal("Error: Could not save town image. Town data is missing.").setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
            }
        }
    }

    /**
     * Draws a circle outline.
     */
    private void drawCircle(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Draw a simple circle using multiple small rectangles
        for (int angle = 0; angle < 360; angle += 2) {
            double radians = Math.toRadians(angle);
            int x = (int) (centerX + radius * Math.cos(radians));
            int y = (int) (centerY + radius * Math.sin(radians));
            context.fill(x, y, x + 1, y + 1, color);
        }
    }

    /**
     * Opens a file upload dialog for selecting an image.
     */
    private void openFileUploadDialog() {
        // Create upload directory if it doesn't exist
        createUploadDirectory();

        // Scan for images in upload directory
        scanUploadDirectory();
    }

    /**
     * Creates the upload directory if it doesn't exist.
     */
    private void createUploadDirectory() {
        try {
            Path uploadDir = Paths.get("config/pokecobbleclaim/upload");
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Created upload directory: " + uploadDir);
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error creating upload directory: " + e.getMessage());
        }
    }

    /**
     * Scans the upload directory for new images and processes them.
     */
    private void scanUploadDirectory() {
        try {
            Path uploadDir = Paths.get("config/pokecobbleclaim/upload");
            if (!Files.exists(uploadDir)) {
                return;
            }

            List<Path> imageFiles = Files.list(uploadDir)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return fileName.endsWith(".png") || fileName.endsWith(".jpg") || fileName.endsWith(".jpeg");
                })
                .toList();

            if (imageFiles.isEmpty()) {
                if (this.client != null && this.client.player != null) {
                    this.client.player.sendMessage(
                        Text.literal("No images found in upload directory.")
                            .setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                }
                return;
            }

            // Process the first image found
            Path firstImage = imageFiles.get(0);
            java.io.File imageFile = firstImage.toFile();

            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("Found image: " + firstImage.getFileName() + ". Processing...")
                        .setStyle(Style.EMPTY.withColor(Formatting.GREEN)), false);
            }

            processSelectedImage(imageFile);

            // Delete the processed file
            try {
                Files.delete(firstImage);
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Deleted processed upload file: " + firstImage);
            } catch (Exception e) {
                com.pokecobble.Pokecobbleclaim.LOGGER.warn("Could not delete upload file: " + e.getMessage());
            }

        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error scanning upload directory: " + e.getMessage());
        }
    }

    /**
     * Processes the selected image file.
     */
    private void processSelectedImage(java.io.File imageFile) {
        try {
            // Validate file size (max 5MB)
            long maxSize = 5 * 1024 * 1024; // 5MB
            if (imageFile.length() > maxSize) {
                if (this.client != null && this.client.player != null) {
                    this.client.player.sendMessage(
                        Text.literal("Error: Image file is too large. Maximum size is 5MB.")
                            .setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                }
                return;
            }

            // Read the image file
            byte[] imageData = java.nio.file.Files.readAllBytes(imageFile.toPath());

            // Generate a unique filename
            String fileName = "uploaded_" + System.currentTimeMillis();
            String extension = getFileExtension(imageFile.getName());

            // Send the image to the server
            sendImageToServer(fileName, extension, imageData);

        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error processing selected image: " + e.getMessage());
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("Error: Could not process selected image. " + e.getMessage())
                        .setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
            }
        }
    }

    /**
     * Gets the file extension from a filename.
     */
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot);
        }
        return ".png"; // Default extension
    }

    /**
     * Sends the image data to the server.
     */
    private void sendImageToServer(String fileName, String extension, byte[] imageData) {
        try {
            if (town == null) {
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot send image: town is null");
                return;
            }

            com.pokecobble.Pokecobbleclaim.LOGGER.info("Sending image to server: " + fileName + extension + " (" + imageData.length + " bytes)");

            // Step 1: Send upload request
            net.minecraft.network.PacketByteBuf requestBuf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            requestBuf.writeUuid(town.getId());
            requestBuf.writeString(fileName, com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            requestBuf.writeString(extension, com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            requestBuf.writeInt(imageData.length);

            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.town.network.NetworkConstants.TOWN_IMAGE_UPLOAD_REQUEST, requestBuf);

            // Step 2: Send image data in chunks
            int chunkSize = 8192; // 8KB chunks
            int totalChunks = (int) Math.ceil((double) imageData.length / chunkSize);

            for (int i = 0; i < totalChunks; i++) {
                int offset = i * chunkSize;
                int currentChunkSize = Math.min(chunkSize, imageData.length - offset);
                byte[] chunk = new byte[currentChunkSize];
                System.arraycopy(imageData, offset, chunk, 0, currentChunkSize);

                net.minecraft.network.PacketByteBuf chunkBuf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
                chunkBuf.writeInt(i); // chunk index
                chunkBuf.writeInt(currentChunkSize);
                chunkBuf.writeBytes(chunk);

                net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                    com.pokecobble.town.network.NetworkConstants.TOWN_IMAGE_UPLOAD_CHUNK, chunkBuf);
            }

            // Step 3: Send completion signal
            net.minecraft.network.PacketByteBuf completeBuf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.town.network.NetworkConstants.TOWN_IMAGE_UPLOAD_COMPLETE, completeBuf);

            // Show success message to user
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("Uploading image... Please wait.")
                        .setStyle(Style.EMPTY.withColor(Formatting.GREEN)), false);
            }

        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error sending image to server: " + e.getMessage());
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("Error: Could not upload image. " + e.getMessage())
                        .setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
            }
        }
    }

}
