package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.town.util.TownImageUtil;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Handles synchronization of town image data between server and clients.
 * This class is responsible for efficiently sending town image updates to the appropriate players.
 * Optimized to reduce log spam and prevent unnecessary operations.
 */
public class TownImageSynchronizer {
    // Constants for town image update packets
    public static final Identifier TOWN_IMAGE_UPDATE = new Identifier(NetworkConstants.MOD_ID, "town_image_update");
    public static final Identifier TOWN_IMAGE_SETTINGS_UPDATE = new Identifier(NetworkConstants.MOD_ID, "town_image_settings_update");
    public static final Identifier REQUEST_TOWN_IMAGE_UPDATE = new Identifier(NetworkConstants.MOD_ID, "request_town_image_update");

    // Cache to track which players have which version of town image data
    private static final Map<UUID, Map<UUID, Integer>> PLAYER_TOWN_IMAGE_VERSIONS = new HashMap<>();

    // Cache for recent log messages to prevent spam
    private static final Map<String, Long> LOG_CACHE = new ConcurrentHashMap<>();
    private static final long LOG_COOLDOWN_MS = 5000; // 5 seconds cooldown for duplicate logs

    /**
     * Logs a debug message only if it hasn't been logged recently.
     */
    private static void logDebugOnce(String message) {
        String key = "DEBUG:" + message;
        long now = System.currentTimeMillis();
        Long lastLogged = LOG_CACHE.get(key);

        if (lastLogged == null || (now - lastLogged) > LOG_COOLDOWN_MS) {
            LOG_CACHE.put(key, now);
            Pokecobbleclaim.LOGGER.debug(message);
        }
    }

    /**
     * Logs an info message only if it hasn't been logged recently.
     */
    private static void logInfoOnce(String message) {
        String key = "INFO:" + message;
        long now = System.currentTimeMillis();
        Long lastLogged = LOG_CACHE.get(key);

        if (lastLogged == null || (now - lastLogged) > LOG_COOLDOWN_MS) {
            LOG_CACHE.put(key, now);
            Pokecobbleclaim.LOGGER.info(message);
        }
    }

    /**
     * Registers client-side packet handlers for town image synchronization.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register town image update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                TOWN_IMAGE_UPDATE,
                TownImageSynchronizer::handleTownImageUpdate
        );

        // Register town image settings update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                TOWN_IMAGE_SETTINGS_UPDATE,
                TownImageSynchronizer::handleTownImageSettingsUpdate
        );

        // Register image upload response handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_IMAGE_UPLOAD_RESPONSE,
                TownImageSynchronizer::handleImageUploadResponse
        );
    }

    /**
     * Registers server-side packet handlers for town image synchronization.
     */
    public static void registerServerHandlers() {
        // Register request town image update handler
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
                REQUEST_TOWN_IMAGE_UPDATE,
                TownImageSynchronizer::handleRequestTownImageUpdate
        );

        // Register town image settings update handler (client to server)
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
                TOWN_IMAGE_SETTINGS_UPDATE,
                TownImageSynchronizer::handleClientImageSettingsUpdate
        );

        // Register comprehensive town image selection update handler
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_IMAGE_SELECTION_UPDATE,
                TownImageSynchronizer::handleTownImageSelectionUpdate
        );

        // Register image upload handlers
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_IMAGE_UPLOAD_REQUEST,
                TownImageSynchronizer::handleImageUploadRequest
        );
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_IMAGE_UPLOAD_CHUNK,
                TownImageSynchronizer::handleImageUploadChunk
        );
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_IMAGE_UPLOAD_COMPLETE,
                TownImageSynchronizer::handleImageUploadComplete
        );
    }

    /**
     * Requests town image data from the server.
     * This should be called when a client needs to refresh town image data.
     *
     * @param townId The ID of the town to request data for, or null for all towns
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownImageUpdate(UUID townId) {
        try {
            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Write town ID (or null for all towns)
            if (townId != null) {
                buf.writeBoolean(true);
                buf.writeUuid(townId);
            } else {
                buf.writeBoolean(false);
            }

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(REQUEST_TOWN_IMAGE_UPDATE, buf);

            logDebugOnce("Requested town image update for " + (townId != null ? townId : "all towns"));
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town image update: " + e.getMessage());
        }
    }

    /**
     * Handles requests for town image updates on the server side.
     */
    private static void handleRequestTownImageUpdate(MinecraftServer server, ServerPlayerEntity player,
                                                   net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                   PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read if request is for a specific town
            boolean isSpecificTown = buf.readBoolean();

            if (isSpecificTown) {
                // Read town ID
                UUID townId = buf.readUuid();

                // Get town
                Town town = TownManager.getInstance().getTownById(townId);
                if (town != null) {
                    // Send town image update
                    sendTownImageUpdate(player, town);

                    // Send town image settings update safely
                    String imageName = town.getImage();
                    if (imageName != null && !imageName.isEmpty() && !imageName.equals("default")) {
                        sendTownImageSettingsUpdateSafe(player, town, imageName);
                    }
                }
            } else {
                // Send updates for all towns the player should know about
                for (Town town : TownManager.getInstance().getAllTowns()) {
                    if (shouldSendTownImageUpdate(player.getUuid(), town)) {
                        // Send town image update
                        sendTownImageUpdate(player, town);

                        // Send town image settings update safely
                        String imageName = town.getImage();
                        if (imageName != null && !imageName.isEmpty() && !imageName.equals("default")) {
                            sendTownImageSettingsUpdateSafe(player, town, imageName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town image update request: " + e.getMessage());
        }
    }

    /**
     * Synchronizes town image data to all relevant players.
     * This method efficiently sends only the changed data to the players who need it.
     *
     * @param server The server instance
     * @param town The town to synchronize
     */
    public static void syncTownImageData(MinecraftServer server, Town town) {
        if (town == null) {
            return;
        }

        // Check if image data has changed
        if (!town.hasChanged(Town.ASPECT_IMAGE)) {
            return;
        }

        // Get all online players
        List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();

        // Determine which players need updates
        for (ServerPlayerEntity player : onlinePlayers) {
            UUID playerId = player.getUuid();

            // Check if this player needs an update for this town
            if (shouldSendTownImageUpdate(playerId, town)) {
                // Send town image update
                sendTownImageUpdate(player, town);

                // Update the player's known version for this town's image
                updatePlayerTownImageVersion(playerId, town.getId(), town.getDataVersion());
            }
        }
    }

    /**
     * Forces town image data sync to a specific player.
     * This method sends image data regardless of change status, useful for player join events.
     *
     * @param player The player to send data to
     * @param town The town to synchronize
     */
    public static void forceTownImageSyncToPlayer(ServerPlayerEntity player, Town town) {
        if (player == null || town == null) {
            return;
        }

        try {
            // Send town image update
            sendTownImageUpdate(player, town);

            // Send town image settings update only if the town has an image
            String imageName = town.getImage();
            if (imageName != null && !imageName.isEmpty() && !imageName.equals("default")) {
                // Use the safer method that doesn't trigger client-side class loading
                sendTownImageSettingsUpdateSafe(player, town, imageName);
            }

            // Update the player's known version for this town's image
            updatePlayerTownImageVersion(player.getUuid(), town.getId(), town.getDataVersion());

            logDebugOnce("Forced town image sync for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error forcing town image sync to player: " + e.getMessage());
        }
    }

    /**
     * Synchronizes town image settings to all relevant players.
     * This should be called when a town's image settings change.
     *
     * @param server The server instance
     * @param town The town whose image settings have changed
     * @param imageName The name of the image whose settings have changed
     */
    public static void syncTownImageSettings(MinecraftServer server, Town town, String imageName) {
        if (town == null || imageName == null || imageName.isEmpty()) {
            return;
        }

        // Get all online players
        List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();

        // Send updates to relevant players
        for (ServerPlayerEntity player : onlinePlayers) {
            // Check if this player should receive updates for this town
            if (shouldSendTownImageUpdate(player.getUuid(), town)) {
                // Send town image settings update safely
                sendTownImageSettingsUpdateSafe(player, town, imageName);
            }
        }
    }

    /**
     * Checks if a player should receive updates for a town's image.
     *
     * @param playerId The player's UUID
     * @param town The town
     * @return True if the player should receive updates, false otherwise
     */
    private static boolean shouldSendTownImageUpdate(UUID playerId, Town town) {
        // Players should receive updates for:
        // 1. Their own town
        // 2. Towns they have visited or interacted with

        // Check if player is in the town
        if (town.getPlayers().contains(playerId)) {
            return true;
        }

        // Check if player has a different version than the current one
        Map<UUID, Integer> playerVersions = PLAYER_TOWN_IMAGE_VERSIONS.get(playerId);
        if (playerVersions != null) {
            Integer knownVersion = playerVersions.get(town.getId());
            if (knownVersion == null || knownVersion < town.getDataVersion()) {
                return true;
            }
        }

        // By default, don't send updates
        return false;
    }

    /**
     * Updates a player's known version for a town's image.
     *
     * @param playerId The player's UUID
     * @param townId The town's UUID
     * @param version The new version
     */
    private static void updatePlayerTownImageVersion(UUID playerId, UUID townId, int version) {
        // Get or create player versions map
        Map<UUID, Integer> playerVersions = PLAYER_TOWN_IMAGE_VERSIONS.computeIfAbsent(playerId, k -> new HashMap<>());

        // Update version
        playerVersions.put(townId, version);
    }

    /**
     * Sends town image data to a player.
     *
     * @param player The player to send data to
     * @param town The town
     */
    private static void sendTownImageUpdate(ServerPlayerEntity player, Town town) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write town data version
            buf.writeInt(town.getDataVersion());

            // Write town image name
            String imageName = town.getImage();
            buf.writeString(imageName != null ? imageName : "default", NetworkConstants.MAX_STRING_LENGTH);

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_IMAGE_UPDATE, buf);

            logDebugOnce("Sent town image update for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town image update: " + e.getMessage());
        }
    }

    /**
     * Sends town image settings to a player.
     *
     * @param player The player to send data to
     * @param town The town
     */
    private static void sendTownImageSettingsUpdate(ServerPlayerEntity player, Town town) {
        // Get the current image name
        String imageName = town.getImage();
        if (imageName == null || imageName.isEmpty() || imageName.equals("default")) {
            return;
        }

        // Send settings for the current image safely
        sendTownImageSettingsUpdateSafe(player, town, imageName);
    }

    /**
     * Safely sends settings for a specific town image to a player.
     * This method avoids client-side class loading issues by getting settings directly from town settings.
     *
     * @param player The player to send data to
     * @param town The town
     * @param imageName The name of the image
     */
    private static void sendTownImageSettingsUpdateSafe(ServerPlayerEntity player, Town town, String imageName) {
        try {
            // Get image settings directly from town settings to avoid client-side class loading
            Map<String, Object> townSettings = com.pokecobble.town.config.TownSettingsManager.getTownSettings(town.getId());
            if (townSettings == null || !townSettings.containsKey("imageSettings")) {
                // No settings to send
                return;
            }

            Object imageSettingsObj = townSettings.get("imageSettings");
            if (!(imageSettingsObj instanceof Map)) {
                return;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> imageSettingsMap = (Map<String, Object>) imageSettingsObj;

            float scale = 1.0f;
            int offsetX = 0;
            int offsetY = 0;

            if (imageSettingsMap.containsKey("scale")) {
                scale = ((Number) imageSettingsMap.get("scale")).floatValue();
            }
            if (imageSettingsMap.containsKey("offsetX")) {
                offsetX = ((Number) imageSettingsMap.get("offsetX")).intValue();
            }
            if (imageSettingsMap.containsKey("offsetY")) {
                offsetY = ((Number) imageSettingsMap.get("offsetY")).intValue();
            }

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write image name
            buf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);

            // Write settings
            buf.writeFloat(scale);
            buf.writeInt(offsetX);
            buf.writeInt(offsetY);

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_IMAGE_SETTINGS_UPDATE, buf);

            logDebugOnce("Sent town image settings update (safe) for " + town.getName() + ":" + imageName + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town image settings update (safe): " + e.getMessage());
        }
    }

    /**
     * Sends settings for a specific town image to a player.
     *
     * @param player The player to send data to
     * @param town The town
     * @param imageName The name of the image
     */
    private static void sendTownImageSettingsUpdate(ServerPlayerEntity player, Town town, String imageName) {
        try {
            // Get image settings
            TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);
            if (settings == null) {
                // No settings to send
                return;
            }

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write image name
            buf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);

            // Write settings
            buf.writeFloat(settings.scale);
            buf.writeInt(settings.offsetX);
            buf.writeInt(settings.offsetY);

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_IMAGE_SETTINGS_UPDATE, buf);

            logDebugOnce("Sent town image settings update for " + town.getName() + ":" + imageName + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town image settings update: " + e.getMessage());
        }
    }

    /**
     * Handles town image update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownImageUpdate(net.minecraft.client.MinecraftClient client,
                                             net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read town data version
            int dataVersion = buf.readInt();

            // Read town image name
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Get or create town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received image update for unknown town: " + townId);
                return;
            }

            // Update town image
            town.setImage(imageName);

            // Set data version
            town.setDataVersion(dataVersion);

            // Clear changed aspects since we just updated
            town.clearChangedAspects();

            // Mark the town as changed to trigger UI updates
            town.markChanged(Town.ASPECT_IMAGE);

            // Update client cache
            com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, dataVersion);

            // Refresh UI components to update town images
            com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();

            logInfoOnce("Received town image update for " + town.getName() + ": " + imageName + " (data version: " + dataVersion + ")");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town image update: " + e.getMessage());
        }
    }

    /**
     * Handles image upload response packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleImageUploadResponse(net.minecraft.client.MinecraftClient client,
                                                net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                PacketByteBuf buf,
                                                PacketSender responseSender) {
        try {
            boolean success = buf.readBoolean();
            String message = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Show message to player
            if (client.player != null) {
                net.minecraft.text.Style style = success ?
                    net.minecraft.text.Style.EMPTY.withColor(net.minecraft.util.Formatting.GREEN) :
                    net.minecraft.text.Style.EMPTY.withColor(net.minecraft.util.Formatting.RED);

                client.player.sendMessage(
                    net.minecraft.text.Text.literal(message).setStyle(style), false);
            }

            // If successful, refresh the image screen
            if (success && client.currentScreen instanceof com.pokecobble.town.gui.TownImageScreen) {
                com.pokecobble.town.gui.TownImageScreen screen = (com.pokecobble.town.gui.TownImageScreen) client.currentScreen;
                // Reload available images
                screen.loadAvailableImages();
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling image upload response: " + e.getMessage());
        }
    }

    /**
     * Handles town image settings update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownImageSettingsUpdate(net.minecraft.client.MinecraftClient client,
                                                    net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                    PacketByteBuf buf,
                                                    PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read image name
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read settings
            float scale = buf.readFloat();
            int offsetX = buf.readInt();
            int offsetY = buf.readInt();

            // Get town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received image settings update for unknown town: " + townId);
                return;
            }

            // Apply settings locally
            TownImageUtil.applyImageSettingsLocally(town, imageName, scale, offsetX, offsetY);

            // Update client cache
            com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, town.getDataVersion());

            // Refresh UI components to update town image settings
            com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();

            logDebugOnce("Received town image settings update for " + town.getName() + ":" + imageName +
                " (scale: " + scale + ", offset: " + offsetX + "," + offsetY + ")");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town image settings update: " + e.getMessage());
        }
    }

    /**
     * Handles client-to-server town image settings update packets.
     * This is called when a client sends updated image settings to the server.
     */
    private static void handleClientImageSettingsUpdate(MinecraftServer server, ServerPlayerEntity player,
                                                      net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                      PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town UUID
            UUID townId = buf.readUuid();

            // Read image name
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read settings
            float scale = buf.readFloat();
            int offsetX = buf.readInt();
            int offsetY = buf.readInt();

            // Validate parameters
            scale = Math.max(0.1f, Math.min(scale, 5.0f)); // Limit scale between 0.1 and 5.0
            offsetX = Math.max(-500, Math.min(offsetX, 500)); // Limit offset between -500 and 500
            offsetY = Math.max(-500, Math.min(offsetY, 500)); // Limit offset between -500 and 500

            // Get town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received image settings update for unknown town: " + townId);
                return;
            }

            // Security check: Verify that the player is in the town and has permission to change town settings
            if (!town.getPlayers().contains(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " attempted to update image settings for town they're not in: " + town.getName());
                return;
            }

            // Check if player has permission to change town settings
            TownPlayer townPlayer = town.getPlayer(player.getUuid());
            if (townPlayer == null || (!townPlayer.hasPermission("Town Settings", "Can change town image") &&
                                      townPlayer.getRank() != com.pokecobble.town.TownPlayerRank.OWNER)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " attempted to update image settings without permission");
                return;
            }

            // Update town image settings
            TownImageUtil.saveImageSettings(town, imageName, scale, offsetX, offsetY);

            // Sync to all relevant clients
            syncTownImageSettings(server, town, imageName);

            logInfoOnce("Player " + player.getName().getString() + " updated image settings for " + town.getName() + ":" + imageName);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling client image settings update: " + e.getMessage());
        }
    }

    /**
     * Handles comprehensive town image selection update from client.
     * This method receives the complete image selection data and synchronizes it to all players.
     */
    private static void handleTownImageSelectionUpdate(MinecraftServer server,
                                                     ServerPlayerEntity player,
                                                     net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                     PacketByteBuf buf,
                                                     PacketSender sender) {
        try {
            // Read packet data
            UUID townId = buf.readUuid();
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            float scale = buf.readFloat();
            int offsetX = buf.readInt();
            int offsetY = buf.readInt();

            // Validate packet data
            try {
                PacketValidator.validateString(imageName, NetworkConstants.MAX_STRING_LENGTH);
                PacketValidator.validateUUID(townId);

                // Basic validation for numeric values
                if (Float.isNaN(scale) || Float.isInfinite(scale) || scale <= 0 || scale > 10) {
                    throw new IllegalArgumentException("Invalid scale value: " + scale);
                }
                if (Math.abs(offsetX) > 1000 || Math.abs(offsetY) > 1000) {
                    throw new IllegalArgumentException("Invalid offset values: " + offsetX + ", " + offsetY);
                }
            } catch (IllegalArgumentException e) {
                Pokecobbleclaim.LOGGER.warn("Invalid town image selection data from player " + player.getName().getString() + ": " + e.getMessage());
                return;
            }

            // Get the town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to update image for unknown town: " + townId);
                return;
            }

            // Verify player has permission to modify this town
            if (!town.getPlayers().contains(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to update image for town they don't belong to: " + town.getName());
                return;
            }

            // Update town image
            town.setImage(imageName);
            town.markChanged(Town.ASPECT_IMAGE);

            // Save image settings to town settings system
            Map<String, Object> imageSettingsMap = new HashMap<>();
            imageSettingsMap.put("scale", scale);
            imageSettingsMap.put("offsetX", offsetX);
            imageSettingsMap.put("offsetY", offsetY);

            // Update town settings
            com.pokecobble.town.config.TownSettingsManager.setTownSetting(townId, "image", imageName);
            com.pokecobble.town.config.TownSettingsManager.setTownSetting(townId, "imageSettings", imageSettingsMap);

            // Synchronize to all online players immediately
            syncTownImageToAllPlayers(server, town, imageName, scale, offsetX, offsetY);

            logInfoOnce("Player " + player.getName().getString() + " updated image for town " + town.getName() +
                                      " to " + imageName + " with settings: scale=" + scale + ", offset=(" + offsetX + "," + offsetY + ")");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town image selection update from " + player.getName().getString() + ": " + e.getMessage());
        }
    }

    /**
     * Synchronizes town image data to all online players.
     */
    private static void syncTownImageToAllPlayers(MinecraftServer server, Town town, String imageName, float scale, int offsetX, int offsetY) {
        try {
            // Send to all online players
            for (ServerPlayerEntity onlinePlayer : server.getPlayerManager().getPlayerList()) {
                // Send basic town image update
                sendTownImageUpdate(onlinePlayer, town);

                // Send image settings update
                sendTownImageSettingsUpdateDirect(onlinePlayer, town, imageName, scale, offsetX, offsetY);
            }

            logDebugOnce("Synchronized town image " + imageName + " for town " + town.getName() + " to all online players");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing town image to all players: " + e.getMessage());
        }
    }

    /**
     * Sends town image settings directly with provided values.
     */
    private static void sendTownImageSettingsUpdateDirect(ServerPlayerEntity player, Town town, String imageName, float scale, int offsetX, int offsetY) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write image name
            buf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);

            // Write settings
            buf.writeFloat(scale);
            buf.writeInt(offsetX);
            buf.writeInt(offsetY);

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_IMAGE_SETTINGS_UPDATE, buf);

            logDebugOnce("Sent direct town image settings update for " + town.getName() + ":" + imageName + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending direct town image settings update: " + e.getMessage());
        }
    }

    /**
     * Clears a player's town image version cache.
     * This should be called when a player logs out.
     *
     * @param playerId The player's UUID
     */
    public static void clearPlayerCache(UUID playerId) {
        PLAYER_TOWN_IMAGE_VERSIONS.remove(playerId);
    }

    // Image upload handling
    private static final Map<UUID, ImageUploadSession> UPLOAD_SESSIONS = new ConcurrentHashMap<>();
    private static final int MAX_CHUNK_SIZE = 8192; // 8KB chunks

    /**
     * Handles image upload request from client.
     */
    private static void handleImageUploadRequest(MinecraftServer server, ServerPlayerEntity player,
                                               net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                               PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            UUID townId = buf.readUuid();
            String fileName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            String extension = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            int totalSize = buf.readInt();

            // Validate player is in the town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null || !town.getPlayers().contains(player.getUuid())) {
                sendUploadResponse(player, false, "You are not a member of this town");
                return;
            }

            // Validate file size (max 5MB)
            if (totalSize > 5 * 1024 * 1024) {
                sendUploadResponse(player, false, "File too large (max 5MB)");
                return;
            }

            // Create upload session
            ImageUploadSession session = new ImageUploadSession(townId, fileName, extension, totalSize);
            UPLOAD_SESSIONS.put(player.getUuid(), session);

            // Send success response
            sendUploadResponse(player, true, "Upload session started");

            Pokecobbleclaim.LOGGER.info("Started image upload session for player " + player.getName().getString() +
                " town " + town.getName() + " file " + fileName + extension);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling image upload request: " + e.getMessage());
            sendUploadResponse(player, false, "Server error: " + e.getMessage());
        }
    }

    /**
     * Handles image upload chunk from client.
     */
    private static void handleImageUploadChunk(MinecraftServer server, ServerPlayerEntity player,
                                             net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                             PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            int chunkIndex = buf.readInt();
            int chunkSize = buf.readInt();
            byte[] chunkData = new byte[chunkSize];
            buf.readBytes(chunkData);

            ImageUploadSession session = UPLOAD_SESSIONS.get(player.getUuid());
            if (session == null) {
                sendUploadResponse(player, false, "No active upload session");
                return;
            }

            // Add chunk to session
            session.addChunk(chunkIndex, chunkData);

            Pokecobbleclaim.LOGGER.debug("Received chunk " + chunkIndex + " (" + chunkSize + " bytes) for " + session.fileName);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling image upload chunk: " + e.getMessage());
            sendUploadResponse(player, false, "Error processing chunk: " + e.getMessage());
        }
    }

    /**
     * Handles image upload completion from client.
     */
    private static void handleImageUploadComplete(MinecraftServer server, ServerPlayerEntity player,
                                                net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            ImageUploadSession session = UPLOAD_SESSIONS.remove(player.getUuid());
            if (session == null) {
                sendUploadResponse(player, false, "No active upload session");
                return;
            }

            // Assemble the complete image
            byte[] completeImage = session.assembleImage();
            if (completeImage == null) {
                sendUploadResponse(player, false, "Failed to assemble image data");
                return;
            }

            // Save the image to the town directory
            Town town = TownManager.getInstance().getTownById(session.townId);
            if (town == null) {
                sendUploadResponse(player, false, "Town not found");
                return;
            }

            boolean saved = saveUploadedImage(town, session.fileName, session.extension, completeImage);
            if (!saved) {
                sendUploadResponse(player, false, "Failed to save image");
                return;
            }

            // Update town image
            town.setImage(session.fileName);
            town.markChanged(Town.ASPECT_IMAGE);

            // Save town settings
            com.pokecobble.town.config.TownSettingsManager.setTownSetting(town.getId(), "image", session.fileName);

            // Synchronize to all players
            syncTownImageToAllPlayers(server, town, session.fileName, 1.0f, 0, 0);

            sendUploadResponse(player, true, "Image uploaded successfully");

            Pokecobbleclaim.LOGGER.info("Successfully uploaded image " + session.fileName + session.extension +
                " for town " + town.getName());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling image upload completion: " + e.getMessage());
            sendUploadResponse(player, false, "Error completing upload: " + e.getMessage());
        }
    }

    /**
     * Sends upload response to client.
     */
    private static void sendUploadResponse(ServerPlayerEntity player, boolean success, String message) {
        try {
            PacketByteBuf buf = NetworkManager.createPacket();
            buf.writeBoolean(success);
            buf.writeString(message, NetworkConstants.MAX_STRING_LENGTH);
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_IMAGE_UPLOAD_RESPONSE, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending upload response: " + e.getMessage());
        }
    }

    /**
     * Saves uploaded image to town directory.
     */
    private static boolean saveUploadedImage(Town town, String fileName, String extension, byte[] imageData) {
        try {
            // Create town directory if it doesn't exist
            Path townDir = Paths.get("config/pokecobbleclaim/towns/" + town.getName());
            if (!Files.exists(townDir)) {
                Files.createDirectories(townDir);
            }

            // Save the image file
            Path imagePath = townDir.resolve(fileName + extension);
            Files.write(imagePath, imageData);

            Pokecobbleclaim.LOGGER.info("Saved uploaded image: " + imagePath);
            return true;

        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Error saving uploaded image: " + e.getMessage());
            return false;
        }
    }

    /**
     * Image upload session class.
     */
    private static class ImageUploadSession {
        final UUID townId;
        final String fileName;
        final String extension;
        final int totalSize;
        final Map<Integer, byte[]> chunks = new HashMap<>();

        public ImageUploadSession(UUID townId, String fileName, String extension, int totalSize) {
            this.townId = townId;
            this.fileName = fileName;
            this.extension = extension;
            this.totalSize = totalSize;
        }

        public void addChunk(int index, byte[] data) {
            chunks.put(index, data);
        }

        public byte[] assembleImage() {
            try {
                // Calculate total chunks needed
                int totalChunks = (int) Math.ceil((double) totalSize / MAX_CHUNK_SIZE);

                // Check if we have all chunks
                if (chunks.size() != totalChunks) {
                    Pokecobbleclaim.LOGGER.error("Missing chunks: expected " + totalChunks + ", got " + chunks.size());
                    return null;
                }

                // Assemble the image
                byte[] result = new byte[totalSize];
                int offset = 0;

                for (int i = 0; i < totalChunks; i++) {
                    byte[] chunk = chunks.get(i);
                    if (chunk == null) {
                        Pokecobbleclaim.LOGGER.error("Missing chunk " + i);
                        return null;
                    }

                    System.arraycopy(chunk, 0, result, offset, chunk.length);
                    offset += chunk.length;
                }

                return result;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error assembling image: " + e.getMessage());
                return null;
            }
        }
    }
}
