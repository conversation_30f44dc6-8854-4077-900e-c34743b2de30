package com.pokecobble.town.network;

import net.minecraft.util.Identifier;

/**
 * Constants for network communication.
 * Centralizes all packet identifiers and network-related constants.
 */
public class NetworkConstants {
    // Mod ID for creating identifiers
    public static final String MOD_ID = "pokecobbleclaim";

    // Town data packets
    public static final Identifier TOWN_DATA_REQUEST = new Identifier(MOD_ID, "town_data_request");
    public static final Identifier TOWN_DATA_RESPONSE = new Identifier(MOD_ID, "town_data_response");
    public static final Identifier TOWN_LIST_REQUEST = new Identifier(MOD_ID, "town_list_request");
    public static final Identifier TOWN_LIST_RESPONSE = new Identifier(MOD_ID, "town_list_response");

    // Town management packets
    public static final Identifier TOWN_CREATE_REQUEST = new Identifier(MOD_ID, "town_create_request");
    public static final Identifier TOWN_CREATE_RESPONSE = new Identifier(MOD_ID, "town_create_response");
    public static final Identifier TOWN_JOIN_REQUEST = new Identifier(MOD_ID, "town_join_request");
    public static final Identifier TOWN_JOIN_RESPONSE = new Identifier(MOD_ID, "town_join_response");
    public static final Identifier TOWN_LEAVE_REQUEST = new Identifier(MOD_ID, "town_leave_request");
    public static final Identifier TOWN_LEAVE_RESPONSE = new Identifier(MOD_ID, "town_leave_response");

    // Town player list updates
    public static final Identifier TOWN_PLAYER_LIST_UPDATE = new Identifier(MOD_ID, "town_player_list_update");

    // Player data packets
    public static final Identifier PLAYER_DATA_REQUEST = new Identifier(MOD_ID, "player_data_request");
    public static final Identifier PLAYER_DATA_RESPONSE = new Identifier(MOD_ID, "player_data_response");
    public static final Identifier PLAYER_PERMISSIONS_UPDATE = new Identifier(MOD_ID, "player_permissions_update");
    public static final Identifier PLAYER_DATA_SYNC = new Identifier(MOD_ID, "player_data_sync");

    // Chunk data packets
    public static final Identifier CHUNK_DATA_REQUEST = new Identifier(MOD_ID, "chunk_data_request");
    public static final Identifier CHUNK_DATA_RESPONSE = new Identifier(MOD_ID, "chunk_data_response");
    public static final Identifier CHUNK_CLAIM_REQUEST = new Identifier(MOD_ID, "chunk_claim_request");
    public static final Identifier CHUNK_CLAIM_RESPONSE = new Identifier(MOD_ID, "chunk_claim_response");

    // Election packets
    public static final Identifier ELECTION_DATA_REQUEST = new Identifier(MOD_ID, "election_data_request");
    public static final Identifier ELECTION_DATA_RESPONSE = new Identifier(MOD_ID, "election_data_response");
    public static final Identifier ELECTION_VOTE_REQUEST = new Identifier(MOD_ID, "election_vote_request");
    public static final Identifier ELECTION_VOTE_RESPONSE = new Identifier(MOD_ID, "election_vote_response");

    // Money packets
    public static final Identifier MONEY_BALANCE_SYNC = new Identifier(MOD_ID, "money_balance_sync");
    public static final Identifier MONEY_BALANCE_REQUEST = new Identifier(MOD_ID, "money_balance_request");
    public static final Identifier MONEY_WATCH_START = new Identifier(MOD_ID, "money_watch_start");
    public static final Identifier MONEY_WATCH_STOP = new Identifier(MOD_ID, "money_watch_stop");
    public static final Identifier MONEY_TRANSFER_REQUEST = new Identifier(MOD_ID, "money_transfer_request");
    public static final Identifier MONEY_TRANSFER_RESPONSE = new Identifier(MOD_ID, "money_transfer_response");

    // Town image packets
    public static final Identifier TOWN_IMAGE_SELECTION_UPDATE = new Identifier(MOD_ID, "town_image_selection_update");
    public static final Identifier TOWN_IMAGE_UPLOAD_REQUEST = new Identifier(MOD_ID, "town_image_upload_request");
    public static final Identifier TOWN_IMAGE_UPLOAD_CHUNK = new Identifier(MOD_ID, "town_image_upload_chunk");
    public static final Identifier TOWN_IMAGE_UPLOAD_COMPLETE = new Identifier(MOD_ID, "town_image_upload_complete");
    public static final Identifier TOWN_IMAGE_UPLOAD_RESPONSE = new Identifier(MOD_ID, "town_image_upload_response");
    public static final Identifier TOWN_IMAGE_DELETE_REQUEST = new Identifier(MOD_ID, "town_image_delete_request");
    public static final Identifier TOWN_IMAGE_DELETE_RESPONSE = new Identifier(MOD_ID, "town_image_delete_response");

    // Security constants
    public static final int MAX_PACKET_SIZE = 32768; // 32KB max packet size
    public static final int MAX_STRING_LENGTH = 8192; // 8KB max string length (increased for town settings)
    public static final int MAX_LIST_SIZE = 1000; // Maximum items in a list
}
